components:
  - name: OTel Receiver_1
    kind: OTelReceiver
  - name: Trace Converter_1
    kind: TraceConverter
  - name: Sample Based on Errors_1
    kind: SampleErrors
    properties:
      - name: ErrorRate
        value: 2
      - name: UserErrorRate
        value: 20
      - name: DefaultRate
        value: 200
  - name: Sample Based on Errors_2
    kind: SampleErrors
    properties:
      - name: ErrorRate
        value: 3
      - name: UserErrorRate
        value: 30
      - name: DefaultRate
        value: 300
  - name: Honeycomb Exporter_1
    kind: HoneycombExporter
connections:
  - source:
      component: OTel Receiver_1
      port: Traces
      type: OTelTraces
    destination:
      component: Trace Converter_1
      port: Traces
      type: OTelTraces
  - source:
      component: Trace Converter_1
      port: Events
      type: HoneycombEvents
    destination:
      component: Sample Based on Errors_1
      port: In
      type: HoneycombEvents
  - source:
      component: Sample Based on Errors_1
      port: Out
      type: HoneycombEvents
    destination:
      component: Sample Based on Errors_2
      port: In
      type: HoneycombEvents
  - source:
      component: Sample Based on Errors_2
      port: Out
      type: HoneycombEvents
    destination:
      component: Honeycomb Exporter_1
      port: Traces
      type: HoneycombEvents
layout:
  components:
    - name: OTel Receiver_1
      position:
        x: -200
        y: -20
    - name: Trace Converter_1
      position:
        x: 160
        y: 0
    - name: Sample Based on Errors_1
      position:
        x: 360
        y: 0
    - name: Sample Based on Errors_2
      position:
        x: 600
        y: 0
    - name: Honeycomb Exporter_1
      position:
        x: 900
        y: 40
