version: 2.1

jobs:
  test:
    docker:
      - image: cimg/go:1.24
    steps:
      - checkout
      - restore_cache:
          keys:
            - v1-go-mod-{{ checksum "go.sum" }}
      - run: make test
      - run: make validate_all
      - store_test_results:
          path: test_results/
      - save_cache:
          key: v1-go-mod-{{ checksum "go.sum" }}
          paths:
            - /home/<USER>/go/pkg/mod

  publish_github:
    docker:
      - image: cibuilds/github:0.13.0
    steps:
      - run:
          name: "Publish Release on GitHub"
          command: |
            echo "Creating GitHub release for tag ${CIRCLE_TAG}"
            ghr -draft -n ${CIRCLE_TAG} -t ${GITHUB_TOKEN} -u ${CIRCLE_PROJECT_USERNAME} -r ${CIRCLE_PROJECT_REPONAME} -c ${CIRCLE_SHA1} ${CIRCLE_TAG}

workflows:
  build:
    jobs:
      - test:
          filters:
            tags:
              only: /.*/
      - publish_github:
          context: Honeycomb Secrets for Public Repos
          requires:
            - test
          filters:
            tags:
              only: /^v[0-9].*/
            branches:
              ignore: /.*/
