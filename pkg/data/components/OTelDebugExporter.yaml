kind: OTelDebugExporter
name: OTel Debug Exporter
style: exporter
logo: opentelemetry
type: base
status: alpha
version: v0.1.0
summary: Sends pipeline signal traffic to stdout for debugging.
description: |
  Exports signal data from a pipeline to stdout. This is useful for debugging, but only if you
  have access to the stdout stream in your environment. This component is not intended for production use.
tags:
  - category:exporter
  - category:debug
  - service:collector
  - signal:OTelTraces
  - signal:OTelMetrics
  - signal:OTelLogs
ports:
  - name: Traces
    direction: input
    type: OTelTraces
  - name: Metrics
    direction: input
    type: OTelMetrics
  - name: Logs
    direction: input
    type: OTelLogs
properties:
  - name: Verbosity
    summary: The verbosity level of the debug output.
    description: |
      The verbosity level of the debug output. Valid values are basic, normal, or detailed. The default is "basic".
    type: string
    subtype: oneof(basic, normal, detailed)
    validations:
      - noblanks
      - oneof(basic, normal, detailed)
    default: basic
templates:
  - kind: collector_config
    name: otel_debug_exporter_collector
    format: collector
    meta:
      componentSection: exporters
      signalTypes: [traces, metrics, logs] # we'll generate a name for each pipeline if there's more than 1
      collectorComponentName: debug
    data:
      - key: "{{ .ComponentName }}.verbosity"
        value: "{{ .Values.Verbosity }}"
