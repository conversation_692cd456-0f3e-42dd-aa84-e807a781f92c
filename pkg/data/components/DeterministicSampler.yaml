kind: DeterministicSampler
name: Deterministic Sampler
style: sampler
type: base
status: alpha
version: v0.1.0
summary: Deterministically samples a fixed fraction of traces based on trace ID.
description: |
  A sampler that deterministically samples a fixed fraction of traces based on trace ID.
tags:
  - category:sampling
  - service:refinery
  - signal:HoneycombEvents
  - sampler:deterministic
  - sampler:rate
ports:
  - name: Traces
    direction: input
    type: HoneycombEvents
  - name: SampledOutput
    direction: output
    type: HoneycombEvents
    note: "The traces that are sampled"
properties:
  - name: Environment
    summary: The environment in which to enable the sampler.
    description: |
      The environment in which to enable the sampler. If this field is
      not specified, the sampler will be enabled in the __default__ environment,
      which is what is used if the environment specified in the trace is not found.
    type: string
    default: "__default__"
  - name: SampleRate
    summary: The target SampleRate.
    description: |
      The target SampleRate. One of every SampleRate traces will be sampled. In other
      words, a sample rate of 5 means that 1 in 5 traces will be sampled. SampleRate
      is the inverse of the sampling probability. A sample rate of 1 means that every
      trace will be sampled.
    type: int
    validations:
      - positive
    default: 100
templates:
  - kind: refinery_rules
    name: DeterministicSampler_RefineryRules
    format: rules
    meta:
      env: "{{ .Values.Environment }}"
      sampler: DeterministicSampler
    data:
      - key: SampleRate
        value: "{{ .Values.SampleRate | encodeAsInt }}"
