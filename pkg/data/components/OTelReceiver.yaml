kind: OTelReceiver
name: OTel Receiver
style: receiver
logo: opentelemetry
type: base
status: alpha
version: v0.1.0
summary: Receives OTLP (OpenTelemetry) traffic via gRPC or HTTP or both.
description: |
  Imports OTLP signals from OpenTelemetry via gRPC or HTTP.
  This receiver can be configured to listen on both gRPC and HTTP, or just one.
tags:
  - category:receiver
  - service:collector
  - signal:OTelTraces
  - signal:OTelMetrics
  - signal:OTelLogs
ports:
  - name: Traces
    direction: output
    type: OTelTraces
  - name: Metrics
    direction: output
    type: OTelMetrics
  - name: Logs
    direction: output
    type: OTelLogs
properties:
  - name: Host
    summary: The hostname or IP address on which to listen
    description: |
      Hostname or IP address on which to listen for incoming traffic.
      It is recommended not to change the default unless
      you know what you're doing.
    type: string
    validations:
      - noblanks
    default: ${STRAWS_COLLECTOR_POD_IP}
    advanced: true
  - name: GRPCPort
    summary: The port on which to listen for gRPC traffic.
    description: |
      The port on which to listen for incoming gRPC traffic.
      The OTel default is 4317. Set to 0 to disable gRPC.
    type: int
    validations:
      - inrange(1, 65535)
    default: 4317
    advanced: true
  - name: HTTPPort
    summary: The port on which to listen for HTTP traffic.
    description: |
      The port on which to listen for incoming HTTP traffic.
      The OTel default is 4318. Set to 0 to disable HTTP.
    type: int
    validations:
      - inrange(1, 65535)
    default: 4318
    advanced: true
templates:
  - kind: collector_config
    name: otel_receiver_collector
    format: collector
    meta:
      componentSection: receivers
      signalTypes: [traces, metrics, logs]
      collectorComponentName: otlp
    data:
      - key: "{{ .ComponentName }}.protocols.grpc.endpoint"
        value: "{{ .Values.Host }}:{{ .Values.GRPCPort }}"
        suppress_if: "{{ eq .HProps.GRPCPort 0 }}"
      - key: "{{ .ComponentName }}.protocols.http.endpoint"
        value: "{{ .Values.Host }}:{{ .Values.HTTPPort }}"
        suppress_if: "{{ eq .HProps.HTTPPort 0 }}"
      # service is not part of the template, it's generated automatically by the collectorConfig
