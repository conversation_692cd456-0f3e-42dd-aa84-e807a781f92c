kind: LogDeduplicationProcessor
name: Log Deduplication Processor
style: processor
type: base
status: development
version: v0.1.0
summary: A processor that removes duplicate log entries.
description: |
  This processor is used to deduplicate logs by detecting identical logs over a range of time and
  emitting a single log with the count of logs that were deduplicated.
tags:
  - category:processor
  - service:collector
  - signal:OTelLogs
ports:
  - name: LogsIn
    direction: input
    type: OTelLogs
  - name: LogsOut
    direction: output
    type: OTelLogs
properties:
  - name: Interval
    type: duration
    validations:
      - duration
    default: 60s
    advanced: true
  - name: CountAttribute
    type: string
    default: sampleRate
    advanced: true
templates:
  - kind: collector_config
    name: otel_logdedup
    format: collector
    meta:
      componentSection: processors
      signalTypes: [logs]
      collectorComponentName: logdedup
    data:
      - key: "{{ .ComponentName }}.interval"
        value: "{{ .Values.Interval }}"
      - key: "{{ .ComponentName }}.log_count_attribute"
        value: "{{ .Values.CountAttribute }}"
