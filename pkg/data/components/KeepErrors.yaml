kind: KeepErrors
name: Keep Errors
style: sampler
type: base
status: development
version: v0.1.0
summary: Keeps traces with errors at a specified rate.
description: |
  Any trace that contains any span with an error field will be sampled
  at the specified rate (default 1). The name of the error field can be
  overridden (default 'error').
tags:
  - category:refinery_rule
  - service:refinery
  - vendor:Honeycomb
  - type:error
ports:
  - name: In
    direction: input
    type: HoneycombEvents
  - name: Out
    direction: output
    type: HoneycombEvents
properties:
  - name: Environment
    summary: The environment in which to enable the sampler.
    description: |
      The environment in which to enable the sampler. If this field is
      not specified, the sampler will be enabled in the __default__ environment,
      which is what is used if the environment specified in the trace is not found.
    type: string
    default: "__default__"
  - name: FieldName
    summary: The name of the field to check for errors.
    description: |
      The name of the field to check (default 'error'). If a field of this name
      exists, the trace will be sampled at the specified rate.
    type: string
    default: "error"
  - name: SampleRate
    summary: The sample rate to use if the trace contains error spans.
    description: |
      The sample rate to use if the rule matches. Example: 10 to keep 1 out of
      10 traces.
    type: int
    default: 1
    validations:
      - positive
templates:
  - kind: refinery_rules
    name: KeepErrors_RefineryRules
    format: rules
    meta:
      env: "{{ .Values.Environment }}"
      sampler: RulesBasedSampler
    data:
      - key: Rules[0].Name
        value: "Keep traces with errors at a sample rate of {{ .Values.SampleRate }}"
      - key: Rules[0].SampleRate
        value: "{{ .Values.SampleRate | encodeAsInt }}"
      - key: "Rules[0].!condition!"
        value: "ix=0;f={{ .Values.FieldName }};o=exists"
