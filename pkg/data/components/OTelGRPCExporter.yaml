kind: OTelGRPCExporter
name: OTel gRPC Exporter
type: base
style: exporter
logo: opentelemetry
status: alpha
version: v0.1.0
summary: Sends telemetry in OTLP (OpenTelemetry) format via gRPC.
description: |
  Exports OpenTelemetry signals using OTLP via gRPC.
tags:
  - category:exporter
  - service:collector
  - signal:OTelTraces
  - signal:OTelMetrics
  - signal:OTelLogs
ports:
  - name: Traces
    direction: input
    type: OTelTraces
  - name: Metrics
    direction: input
    type: OTelMetrics
  - name: Logs
    direction: input
    type: OTelLogs
properties:
  - name: Headers
    summary: Headers to emit when sending gRPC traffic.
    description: |
      Sending data to a backend may require additional headers to be
      configured. This property supports sending a map of header keys and
      values.
    type: map
    subtype: header
  - name: Host
    summary: The hostname or IP address to send data to.
    description: |
      Hostname or IP address on which to send outgoing GRPC traffic.
    type: string
    validations:
      - noblanks
    default: https://api.honeycomb.io
    advanced: true
  - name: Port
    summary: The port on which to send gRPC traffic.
    description: |
      The port on which to send outgoing gRPC traffic. Default is 443, which is
      the value expected by <PERSON><PERSON>. The OTel standard for gRPC is 4317.
    type: int
    validations:
      - inrange(1, 65535)
    default: 443
    advanced: true
  - name: Insecure
    summary: Provide a way to disable TLS export.
    description: |
      Can be used to send data without TLS.
    type: bool
    default: false
    advanced: true
  - name: BatchTimeout
    summary: How long to wait to before sending a batch, regardless of size.
    description: |
      Configure how long to wait before sending a batch. The batch will be sent after
      this timeout.
    type: duration
    default: 200ms
    validations:
      - duration
      - nonempty
    advanced: true
  - name: BatchSize
    summary: The size of a batch.
    description: |
      The size of a batch, measured by span/datapoint/log record count. Once a batch reaches this size it will be sent.
    type: int
    default: 8192
    validations:
      - nonempty
    advanced: true
  - name: QueueSize
    summary: The size of a exporting queue.
    description: |
      The size of the exporting queue, measured by span/datapoint/log record count.
      Items will be kept in the queue while the batch is being created.
    type: int
    default: 100_000
    validations:
      - nonempty
    advanced: true
templates:
  - kind: collector_config
    name: otel_grpc_exporter_collector
    format: collector
    meta:
      componentSection: exporters
      signalTypes: [traces, metrics, logs] # we'll generate a name for each pipeline if there's more than 1
      collectorComponentName: otlp
    data:
      - key: "{{ .ComponentName }}.endpoint"
        value: "{{ .Values.Host }}:{{ .Values.Port }}"
      - key: "{{ .ComponentName }}.tls.insecure"
        value: "{{ .HProps.Insecure | encodeAsBool }}"
        suppress_if: "{{ not .HProps.Insecure }}"
      - key: "{{ .ComponentName }}.headers"
        value: "{{ .HProps.Headers | encodeAsMap }}"
        suppress_if: "{{ not .HProps.Headers }}"
      - key: "{{ .ComponentName }}.sending_queue.queue_size"
        value: "{{ .Values.QueueSize | encodeAsInt }}"
      - key: "{{ .ComponentName }}.sending_queue.enabled"
        value: "{{ true | encodeAsBool}}"
      - key: "{{ .ComponentName }}.sending_queue.sizer"
        value: "items"
      - key: "{{ .ComponentName }}.sending_queue.batch.flush_timeout"
        value: "{{ .Values.BatchTimeout }}"
      - key: "{{ .ComponentName }}.sending_queue.batch.min_size"
        value: "{{ .Values.BatchSize | encodeAsInt }}"
      - key: "{{ .ComponentName }}.sending_queue.batch.max_size"
        value: "{{ .Values.BatchSize | encodeAsInt }}"
      # service is not part of the template, it's generated automatically by the collectorConfig
