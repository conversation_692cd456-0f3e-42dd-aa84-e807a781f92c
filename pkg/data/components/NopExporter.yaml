kind: NopExporter
name: DefaultNopExporter
version: v0.1.0
style: exporter
logo: opentelemetry
type: base
status: alpha
summary: An "exporter" that does nothing, but might be useful for testing.
description: |
  A simple no-op exporter.
  This exporter does nothing. It is required for the minimal collector.
tags:
  - category:exporter
  - category:nop
  - category:debug
  - service:collector
  - signal:OTelTraces
  - signal:OTelMetrics
  - signal:OTelLogs
ports:
  - name: Traces
    direction: input
    type: OTelTraces
  - name: Metrics
    direction: input
    type: OTelMetrics
  - name: Logs
    direction: input
    type: OTelLogs
templates:
  - kind: collector_config
    name: nop_exporter_collector
    format: collector
    meta:
      componentSection: exporters
      signalTypes: [traces, metrics, logs]
      collectorComponentName: nop
    data:
      - key: "{{ .ComponentName }}"
        value: {}
