kind: HoneycombExporter
name: Honeycomb Exporter
style: exporter
logo: honeycomb
type: base
status: alpha
version: v0.1.0
summary: Sends traces to <PERSON><PERSON> in Honeycomb's event format.
description: |
  This component exports traces to <PERSON><PERSON> in Honeycomb's event format.
tags:
  - category:exporter
  - service:refinery
  - signal:HoneycombEvents
  - vendor:Honeycomb
ports:
  - name: Traces
    direction: input
    type: HoneycombEvents
properties:
  - name: APIKey
    summary: The API key to use to authenticate with <PERSON><PERSON>.
    description: |
      The API key to use to authenticate with Honey<PERSON>.
    type: string
    validations:
      - noblanks
    default: ${HONEYCOMB_EXPORTER_APIKEY}
    advanced: true
  - name: APIEndpoint
    summary: The Endpoint URL of the Honeycomb API
    description: |
      The Endpoint URL of the Honeycomb API.
      This is normally https://api.honeycomb.io, but can be overridden.
    type: string
    validations:
      - noblanks
      - url
    default: https://api.honeycomb.io
    advanced: true
  - name: Mode
    summary: Configures when to use the the APIKey.
    description: |
      Allows configuring when the exporter uses the APIKey.
      Valid values are 'all' and 'none'.
      The value 'none' means that the APIKey will
      not be used. Defaults to 'all', which means all
      the traffic will be exported using the configured APIKey.
    type: string
    validations:
      - oneof(all, none)
    default: all
    advanced: true
templates:
  - kind: refinery_config
    name: HoneycombExporter_RefineryConfig
    format: dotted
    data:
      - key: Network.HoneycombAPI
        value: "{{ .Values.APIEndpoint }}"
      - key: AccessKeys.SendKey
        value: "{{ .Values.APIKey }}"
        suppress_if: '{{ eq "none" (or .HProps.APIKey .User.APIKey) }}'
      - key: AccessKeys.SendKeyMode
        value: "{{ .Values.Mode }}"
