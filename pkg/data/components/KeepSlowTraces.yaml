kind: KeepSlowTraces
name: Keep Slow Traces
style: sampler
type: base
status: development
version: v0.1.0
summary: Keeps traces where the root span exceeds a specified duration.
description: |
  This sampler checks the duration of the root span in a trace.
  If the root span exceeds the specified duration, the trace will be sampled
  at the specified rate (default 1).
tags:
  - category:refinery_rule
  - service:refinery
  - vendor:Honeycomb
  - type:error
ports:
  - name: In
    direction: input
    type: HoneycombEvents
  - name: Out
    direction: output
    type: HoneycombEvents
properties:
  - name: Environment
    summary: The environment in which to enable the sampler.
    description: |
      The environment in which to enable the sampler. If this field is
      not specified, the sampler will be enabled in the __default__ environment,
      which is what is used if the environment specified in the trace is not found.
    type: string
    default: "__default__"
  - name: Duration
    summary: Traces longer than this duration will be sampled at the specified rate.
    description: |
      The duration (in milliseconds) that the root span must exceed for the trace
      to be sampled. If the root span's duration is greater than this value, the
      trace will be sampled at the specified rate.
    type: int
    default: 1000
    validations:
      - positive
  - name: SampleRate
    summary: The sample rate to use if the trace exceeds the duration.
    description: |
      The sample rate to use if the rule matches. Example: 10 to keep 1 out of
      10 traces.
    type: int
    default: 1
    validations:
      - positive
templates:
  - kind: refinery_rules
    name: KeepErrors_RefineryRules
    format: rules
    meta:
      env: "{{ .Values.Environment }}"
      sampler: RulesBasedSampler
    data:
      - key: Rules[0].Name
        value: "If a trace lasts longer than {{ .Values.Duration }}, sample at {{ .Values.SampleRate }}"
      - key: Rules[0].SampleRate
        value: "{{ .Values.SampleRate | encodeAsInt }}"
      - key: "Rules[0].!condition!"
        value: "ix=0;f=duration_ms;o=>=;v={{ .Values.Duration }};d=i"
