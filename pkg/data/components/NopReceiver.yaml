kind: NopReceiver
name: DefaultNopReceiver
version: v0.1.0
style: receiver
logo: opentelemetry
type: base
status: alpha
summary: A no-op "receiver" that does nothing, but might be useful for testing.
description: |
  A simple no-op receiver.
  This receiver does nothing. It is required for the minimal configuration.
tags:
  - category:receiver
  - category:nop
  - category:debug
  - service:collector
  - signal:OTelTraces
  - signal:OTelMetrics
  - signal:OTelLogs
ports:
  - name: Traces
    direction: output
    type: OTelTraces
  - name: Metrics
    direction: output
    type: OTelMetrics
  - name: Logs
    direction: output
    type: OTelLogs
templates:
  - kind: collector_config
    name: nop_receiver_collector
    format: collector
    meta:
      componentSection: receivers
      signalTypes: [traces, metrics, logs]
      collectorComponentName: nop
    data:
      - key: "{{ .ComponentName }}"
        value: {}
