name: EMA Throughput Sampling
kind: TemplateEMAThroughput
version: v0.1.0
summary: Limit the throughput of traces to Honey<PERSON> using an EMA sampler
description: |
  Limit the throughput of traces to Honey<PERSON> using an EMA sampler. This template
  accepts traces from an OTel receiver, converts them to Honeycomb format, and
  then samples them using an EMA Throughput Sampler before exporting them to
  Honeycomb. Control the volume of traces by modifying the GoalThroughputPerSec value in
  the properties section of the EMAThroughputSampler component.
components:
  - name: OTel Receiver 1
    kind: OTelReceiver
  - name: Trace Converter 1
    kind: TraceConverter
  - name: EMA Throughput 1
    kind: EMAThroughput
    properties:
      - name: FieldList
        value:
          - http.method
          - http.status_code
  - name: Honeycomb Exporter 1
    kind: HoneycombExporter
connections:
  - source:
      component: OTel Receiver 1
      port: Traces
      type: OTelTraces
    destination:
      component: Trace Converter 1
      port: Input
      type: OTelTraces
  - source:
      component: Trace Converter 1
      port: Output
      type: Honeycomb
    destination:
      component: EMA Throughput 1
      port: Input
      type: Honeycomb
  - source:
      component: EMA Throughput 1
      port: Kept
      type: Honeycomb
    destination:
      component: Honeycomb Exporter 1
      port: Traces
      type: Honeycomb
layout:
  components:
    - name: OTel Receiver 1
      position:
        x: -340
        y: 0
    - name: Trace Converter 1
      position:    
        x: 0
        y: 0
    - name: <PERSON>comb Exporter 1
      position:
        x: 240
        y: 0
    - name: EMA Throughput 1
      position:
        x: 540
        y: 0
