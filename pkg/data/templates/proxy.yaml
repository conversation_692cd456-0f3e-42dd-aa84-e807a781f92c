name: Basic Proxy
kind: TemplateProxy
version: v0.1.0
summary: A basic proxy that receives traces, metrics, and logs and forwards them to the OpenTelemetry GRPC exporter.
description: |
  This is a basic proxy that forwards traces, metrics, and logs received via OpenTelemetry
  components to the OpenTelemetry GRPC exporter. This is useful for testing and debugging, or
  as a starting point for more complex configurations.
components:
  - name: OTel Receiver 1
    kind: OTelReceiver
  - name: OTel gRPC Exporter 1
    kind: OTelGRPCExporter
connections:
  - source:
      component: OTel Receiver 1
      port: Traces
      type: OTelTraces
    destination:
      component: OTel gRPC Exporter 1
      port: Traces
      type: OTelTraces
  - source:
      component: OTel Receiver 1
      port: Metrics
      type: OTelMetrics
    destination:
      component: OTel gRPC Exporter 1
      port: Metrics
      type: OTelMetrics
  - source:
      component: OTel Receiver 1
      port: Logs
      type: OTelLogs
    destination:
      component: OTel gRPC Exporter 1
      port: Logs
      type: OTelLogs
layout:
  components:
    - name: OTel Receiver 1
      position:
        x: -100
        y: 0
    - name: OTel gRPC Exporter 1
      position:
        x: 200
        y: 0
