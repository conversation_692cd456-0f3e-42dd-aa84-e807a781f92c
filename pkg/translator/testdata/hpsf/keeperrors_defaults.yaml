components:
  - name: honeycomb_in
    kind: TraceConverter
  - name: honeycomb_out
    kind: HoneycombExporter
  - name: Sampler_1
    kind: KeepErrors
connections:
  - source:
      component: honeycomb_in
      port: TraceOut
      type: Honeycomb
    destination:
      component: Sampler_1
      port: Input
      type: Honeycomb
  - source:
      component: Sampler_1
      port: Kept
      type: Honeycomb
    destination:
      component: honeycomb_out
      port: Traces
      type: Honeycomb
