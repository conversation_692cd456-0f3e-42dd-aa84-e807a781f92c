components:
  - name: honeycomb_in
    kind: TraceConverter
  - name: honeycomb_out
    kind: HoneycombExporter
  - name: EMAThroughput_1
    kind: EMAThroughput
    properties:
      - name: Environment
        value: test
      - name: AdjustmentInterval
        value: 120
      - name: FieldList
        value: [http.method, http.status_code]
      - name: GoalThroughputPerSec
        value: 42
connections:
  - source:
      component: honeycomb_in
      port: TraceOut
      type: Honeycomb
    destination:
      component: EMAThroughput_1
      port: Input
      type: Honeycomb
  - source:
      component: EMAThroughput_1
      port: Kept
      type: Honeycomb
    destination:
      component: honeycomb_out
      port: Traces
      type: Honeycomb
