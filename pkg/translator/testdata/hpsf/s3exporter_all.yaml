components:
  - name: otlp_in
    kind: OTelReceiver
  - name: s3_out
    kind: S3Exporter
    properties:
      - name: Region
        value: 'my-region'
      - name: Bucket
        value: 'my-bucket'
      - name: Prefix
        value: 'my-prefix'
      - name: PartitionFormat
        value: 'my-partition-format'
      - name: Timeout
        value: 30s
      - name: Marshaler
        value: otlp_json
      - name: BatchTimeout
        value: 30s
      - name: BatchSize
        value: 200_000
      - name: QueueSize
        value: 2_000_000
connections:
  - source:
      component: otlp_in
      port: Traces
      type: OTelTraces
    destination:
      component: s3_out
      port: Traces
      type: OTelTraces
