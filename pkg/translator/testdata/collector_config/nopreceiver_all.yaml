receivers:
    nop/otlp_in: {}
processors:
    usage: {}
exporters:
    otlp/otlp_out:
        endpoint: https://api.honeycomb.io:443
        sending_queue:
            batch:
                flush_timeout: 200ms
                max_size: 8192
                min_size: 8192
            enabled: true
            queue_size: 100000
            sizer: items
extensions:
    honeycomb: {}
service:
    extensions: [honeycomb]
    pipelines:
        logs:
            receivers: [nop/otlp_in]
            processors: [usage]
            exporters: [otlp/otlp_out]
