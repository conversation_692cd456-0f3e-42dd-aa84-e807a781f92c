receivers:
    otlp/otlp_in:
        protocols:
            grpc:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4317
            http:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4318
processors:
    logdedup/DedupMyLogs:
        interval: 60s
        log_count_attribute: sampleRate
    usage: {}
exporters:
    otlp/otlp_out:
        endpoint: https://api.honeycomb.io:443
        sending_queue:
            batch:
                flush_timeout: 200ms
                max_size: 8192
                min_size: 8192
            enabled: true
            queue_size: 100000
            sizer: items
extensions:
    honeycomb: {}
service:
    extensions: [honeycomb]
    pipelines:
        logs:
            receivers: [otlp/otlp_in]
            processors: [usage, logdedup/DedupMyLogs]
            exporters: [otlp/otlp_out]
