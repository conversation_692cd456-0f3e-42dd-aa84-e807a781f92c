receivers:
    otlp/otlp_in:
        protocols:
            grpc:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4317
            http:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4318
processors:
    usage: {}
exporters:
    awss3/s3_out:
        marshaler: otlp_json
        s3uploader:
            compression: gzip
            region: my-region
            s3_bucket: my-bucket
            s3_partition_format: my-partition-format
            s3_prefix: my-prefix
        sending_queue:
            batch:
                flush_timeout: 30s
                max_size: 200000
                min_size: 200000
            enabled: true
            queue_size: 2000000
            sizer: items
        timeout: 30s
extensions:
    honeycomb: {}
service:
    extensions: [honeycomb]
    pipelines:
        traces:
            receivers: [otlp/otlp_in]
            processors: [usage]
            exporters: [awss3/s3_out]
