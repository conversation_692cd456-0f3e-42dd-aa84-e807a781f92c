receivers:
    otlp/otlp_in:
        protocols:
            grpc:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4317
            http:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4318
processors:
    usage: {}
exporters:
    otlphttp/Trace_Converter_1:
        endpoint: http://${STRAWS_REFINERY_SERVICE}:80
        sending_queue:
            batch:
                flush_timeout: 200ms
                max_size: 8192
                min_size: 8192
            enabled: true
            queue_size: 100000
            sizer: items
        tls:
            insecure: true
extensions:
    honeycomb: {}
service:
    extensions: [honeycomb]
    pipelines:
        traces:
            receivers: [otlp/otlp_in]
            processors: [usage]
            exporters: [otlphttp/Trace_Converter_1]
