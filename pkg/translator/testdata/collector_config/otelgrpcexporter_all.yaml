receivers:
    otlp/otlp_in:
        protocols:
            grpc:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4317
            http:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4318
processors:
    usage: {}
exporters:
    otlp/otlp_out:
        endpoint: myhost.com:1234
        headers:
            x-honeycomb-dataset: custom
            x-honeycomb-team: ${HONEYCOMB_API_KEY}
        sending_queue:
            batch:
                flush_timeout: 30s
                max_size: 200000
                min_size: 200000
            enabled: true
            queue_size: 2000000
            sizer: items
        tls:
            insecure: true
extensions:
    honeycomb: {}
service:
    extensions: [honeycomb]
    pipelines:
        traces:
            receivers: [otlp/otlp_in]
            processors: [usage]
            exporters: [otlp/otlp_out]
