receivers:
    otlp/OTel_Receiver_1:
        protocols:
            grpc:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4317
            http:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4318
processors:
    usage: {}
exporters:
    otlphttp/OTel_HTTP_Exporter_1:
        endpoint: https://api.honeycomb.io:443
        headers:
            x-honeycomb-team: ${HONEYCOMB_API_KEY}
        sending_queue:
            batch:
                flush_timeout: 200ms
                max_size: 8192
                min_size: 8192
            enabled: true
            queue_size: 100000
            sizer: items
extensions:
    honeycomb: {}
service:
    extensions: [honeycomb]
    pipelines:
        logs:
            receivers: [otlp/OTel_Receiver_1]
            processors: [usage]
            exporters: [otlphttp/OTel_HTTP_Exporter_1]
        metrics:
            receivers: [otlp/OTel_Receiver_1]
            processors: [usage]
            exporters: [otlphttp/OTel_HTTP_Exporter_1]
        traces:
            receivers: [otlp/OTel_Receiver_1]
            processors: [usage]
            exporters: [otlphttp/OTel_HTTP_Exporter_1]
