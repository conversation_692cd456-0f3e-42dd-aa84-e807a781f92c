RulesVersion: 2
Samplers:
    __default__:
        DeterministicSampler:
            SampleRate: 1
    test:
        RulesBasedSampler:
            Rules:
                - Conditions:
                    - Datatype: int
                      Fields:
                        - http.status_code
                        - http.response.status_code
                      Operator: '>='
                      Value: "500"
                  Name: Sample 500 statuses at 90
                  SampleRate: 90
                - Conditions:
                    - Datatype: int
                      Fields:
                        - http.status_code
                        - http.response.status_code
                      Operator: '>='
                      Value: "400"
                  Name: Sample 400 statuses at 91
                  SampleRate: 91
                - Name: Sample remainder at 92
                  SampleRate: 92
