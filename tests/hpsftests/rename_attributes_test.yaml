name: rename_attributes_test
version: v0.1.0
summary: Test for Rename Attributes Component
description: |
  Test configuration with an OTel receiver connected to a RenameAttributes component that renames
  telemetry attributes, then forwards to an OTel HTTP exporter.

components:
  - name: OTel Receiver 1
    kind: OTelReceiver
  - name: Attribute Renamer
    kind: RenameAttributes
    properties:
      - name: SourceAttributeName
        value: old_attribute_name
      - name: TargetAttributeName
        value: new_attribute_name
  - name: OTel HTTP Exporter 1
    kind: OTelHTTPExporter

connections:
  - source:
      component: OTel Receiver 1
      port: Traces
      type: OTelTraces
    destination:
      component: Attribute Renamer
      port: Traces
      type: OTelTraces
  - source:
      component: Attribute Renamer
      port: Traces
      type: OTelTraces
    destination:
      component: OTel HTTP Exporter 1
      port: Traces
      type: OTelTraces
  - source:
      component: OTel Receiver 1
      port: Metrics
      type: OTelMetrics
    destination:
      component: Attribute Renamer
      port: Metrics
      type: OTelMetrics
  - source:
      component: Attribute Renamer
      port: Metrics
      type: OTelMetrics
    destination:
      component: OTel HTTP Exporter 1
      port: Metrics
      type: OTelMetrics
  - source:
      component: OTel Receiver 1
      port: Logs
      type: OTelLogs
    destination:
      component: Attribute Renamer
      port: Logs
      type: OTelLogs
  - source:
      component: Attribute Renamer
      port: Logs
      type: OTelLogs
    destination:
      component: OTel HTTP Exporter 1
      port: Logs
      type: OTelLogs
