name: keep_errors_test
version: v0.1.0
summary: Test for KeepErrors sampler component
description: |
  Test configuration with an OTel receiver, TraceConverter, KeepErrors sampler, and HoneycombExporter

components:
  - name: OTel Receiver 1
    kind: OTelReceiver
  - name: Trace Converter 1
    kind: TraceConverter
  - name: Error Sampler
    kind: KeepErrors
    properties:
      - name: Environment
        value: production
      - name: FieldName
        value: error_field
      - name: SampleRate
        value: 5
  - name: Honeycomb Exporter 1
    kind: HoneycombExporter

connections:
  - source:
      component: OTel Receiver 1
      port: Traces
      type: OTelTraces
    destination:
      component: Trace Converter 1
      port: TracesIn
      type: OTelTraces
  - source:
      component: Trace Converter 1
      port: TraceOut
      type: Honeycomb
    destination:
      component: Error Sampler
      port: In
      type: Honeycomb
  - source:
      component: Error Sampler
      port: Out
      type: Honeycomb
    destination:
      component: Honeycomb Exporter 1
      port: Traces
      type: Honeycomb
