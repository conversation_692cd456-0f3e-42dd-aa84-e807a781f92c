# Security Policy

This security policy applies to public projects under the [honeycombio organization][gh-organization] on GitHub.
For security reports involving the services provided at `(ui|ui-eu|api|api-eu).honeycomb.io`, refer to the [Honeycomb Bug Bounty Program][bugbounty] for scope, expectations, and reporting procedures.

## Security/Bugfix Versions

Security and bug fixes are generally provided only for the last minor version.
Fixes are released either as part of the next minor version or as an on-demand patch version.

Security fixes are given priority and might be enough to cause a new version to be released.

## Reporting a Vulnerability

We encourage responsible disclosure of security vulnerabilities.
If you find something suspicious, we encourage and appreciate your report!

### Ways to report

In order for the vulnerability reports to reach maintainers as soon as possible, the preferred way is to use the "Report a vulnerability" button under the "Security" tab of the associated GitHub project.
This creates a private communication channel between the reporter and the maintainers.

If you are absolutely unable to or have strong reasons not to use GitHub's vulnerability reporting workflow, please reach out to the Honeycomb security team at [<EMAIL>](mailto:<EMAIL>).

[gh-organization]: https://github.com/honeycombio
[bugbounty]: https://www.honeycomb.io/bugbountyprogram
