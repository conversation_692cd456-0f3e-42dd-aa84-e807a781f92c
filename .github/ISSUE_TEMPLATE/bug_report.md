---
name: Bug report
about: Let us know if something is not working as expected
title: ''
labels: 'type: bug'
assignees: ''

---

<!---
Thank you for taking the time to report bugs!

We love code snippets and links to repositories that reproduce the issue, but understand if you don't have the time to add them. We'll do our best with the info you provide, and might ask follow-up questions.

Please see our [OSS process document](https://github.com/honeycombio/home/<USER>/main/honeycomb-oss-lifecycle-and-practices.md#) to get an idea of how we operate.
--->

**Versions**

<!-- Please include relevant version numbers of things like the Go compiler, this library,
and the code you're using it with. -->

**Steps to reproduce**

<!-- Please be sure to note both actual behavior and what you expected. -->

1.

**Additional context**
