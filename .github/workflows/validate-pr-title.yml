name: "Validate PR Title"

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize

permissions:
  pull-requests: read

jobs:
  main:
    name: Validate PR title
    runs-on: ubuntu-latest
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        id: lint_pr_title
        name: "🤖 Check PR title follows conventional commit spec"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # Have to specify all types because `maint` and `rel` aren't defaults
          types: |
            maint
            rel
            fix
            feat
            chore
            ci
            docs
            style
            refactor
            perf
            test
          ignoreLabels: |
            "type: dependencies"
      # When the previous steps fails, the workflow would stop. By adding this
      # condition you can continue the execution with the populated error message.
      - if: always() && (steps.lint_pr_title.outputs.error_message != null)
        name: "📝 Add PR comment about using conventional commit spec"
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: pr-title-lint-error
          message: |
            Thank you for contributing to the project! 🎉

            We require pull request titles to follow the [Conventional Commits specification](https://www.conventionalcommits.org/en/v1.0.0/) and it looks like your proposed title needs to be adjusted.

            Make sure to prepend with `feat:`, `fix:`, or another option in the list below.

            Once you update the title, this workflow will re-run automatically and validate the updated title.

            Details:

            ```
            ${{ steps.lint_pr_title.outputs.error_message }}
            ```

      # Delete a previous comment when the issue has been resolved
      - if: ${{ steps.lint_pr_title.outputs.error_message == null }}
        name: "❌ Delete PR comment after title has been updated"
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: pr-title-lint-error
          delete: true
